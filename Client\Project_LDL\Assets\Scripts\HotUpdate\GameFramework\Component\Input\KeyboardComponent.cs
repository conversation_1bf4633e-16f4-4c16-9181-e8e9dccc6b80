using UnityEngine;
using UnityGameFramework.Runtime;

namespace Game.Hotfix
{
    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/KeyboardComponent")]
    public class KeyboardComponent : GameFrameworkComponent
    {
        void Start()
        {
            
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                GameEntry.UI.CloseTopUIFormByEsc();
            }
            else if (Input.GetKeyDown(KeyCode.Q))
            {
                GameEntry.Goto.Go(4001);
            }
            else if (Input.GetKeyDown(KeyCode.W))
            {
                GameEntry.Goto.Go(5001);
            }
            else if (Input.GetKeyDown(KeyCode.E))
            {
                GameEntry.Goto.Go(6001);
            }
            else if (Input.GetKeyDown(KeyCode.R))
            {
                GameEntry.Goto.Go(7001);
            }
            else if (Input.GetKeyDown(KeyCode.T))
            {
                GameEntry.PushView.AddPushView(EnumUIForm.UILimitGiftForm, null);
            }
        }
    }
}
