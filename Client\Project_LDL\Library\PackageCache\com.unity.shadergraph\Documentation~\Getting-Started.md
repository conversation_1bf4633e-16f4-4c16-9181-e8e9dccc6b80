# Getting started with Shader Graph

Use Shader Graph with either of the Scriptable Render Pipelines (SRPs) available in Unity version 2018.1 and later:

- The [High Definition Render Pipeline (HDRP)](https://docs.unity3d.com/Packages/com.unity.render-pipelines.high-definition@latest)
- The [Universal Render Pipeline (URP)](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest)

As of Unity version 2021.2, you can also use Shader Graph with the [Built-In Render Pipeline](https://docs.unity3d.com/Documentation/Manual/built-in-render-pipeline.html).

> [!NOTE]
> Shader Graph support for the Built-In Render Pipeline is for compatibility purposes only. Shader Graph doesn't receive updates for Built-In Render Pipeline support, aside from bug fixes for existing features. It's recommended to use Shader Graph with the Scriptable Render Pipelines.

When you install HDRP or URP into your project, Unity also installs the Shader Graph package automatically. You can manually install Shader Graph for use with the Built-In Render Pipeline on Unity version 2021.2 and later with the Package Manager. For more information on how to install a package, see [Adding and removing packages](https://docs.unity3d.com/Manual/upm-ui-actions.html) in the Unity User Manual.

For more information about how to set up a Scriptable Render Pipeline, see [Getting started with HDRP](https://docs.unity3d.com/Packages/com.unity.render-pipelines.high-definition@17.0/manual/getting-started-in-hdrp.html) or [Getting started with URP](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@17.0/manual/InstallingAndConfiguringURP.html).
