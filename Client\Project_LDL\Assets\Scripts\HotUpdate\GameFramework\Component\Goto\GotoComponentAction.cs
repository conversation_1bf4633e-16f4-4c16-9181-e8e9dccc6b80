using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using UnityGameFramework.Runtime;
using Game.Hotfix.Config;
using System.Reflection;

namespace Game.Hotfix
{
    public partial class GotoComponent : GameFrameworkComponent
    {
        #region 通用方法

        /// <summary>
        /// 设置条件结果
        /// </summary>
        /// <param name="condition">条件是否满足</param>
        /// <param name="chainData">跳转链数据</param>
        void SetConditionResult(bool condition, GotoChainData chainData)
        {
            curNext = condition ? chainData.next : chainData.elseNext;
        }

        /// <summary>
        /// 查找建筑模块
        /// </summary>
        /// <param name="buildingId">建筑ID</param>
        /// <returns>建筑模块</returns>
        BuildingModule GetBuildingModule(uint buildingId)
        {
            return GameEntry.LogicData.BuildingData.FindBuildingLevelMaxById(buildingId);
        }

        /// <summary>
        /// 获取商品在列表中的索引
        /// </summary>
        /// <param name="goodsList">商品列表</param>
        /// <param name="targetId">目标ID</param>
        /// <returns>索引</returns>
        int GetItemIndexInList<T>(List<T> list, int targetId, Func<T, int> idSelector)
        {
            for (int i = 0; i < list.Count; i++)
            {
                if (idSelector(list[i]) == targetId)
                {
                    return i;
                }
            }
            return 0;
        }

        /// <summary>
        /// 执行商店滚动定位
        /// </summary>
        /// <param name="tableView">表格视图</param>
        /// <param name="index">索引</param>
        /// <param name="itemsPerRow">每行项目数</param>
        void ExecuteShopScroll(Mosframe.TableView tableView, int index, int itemsPerRow = 1)
        {
            int scrollIndex = itemsPerRow > 1 ? Mathf.FloorToInt(index / (float)itemsPerRow) : Math.Max(index - 1, 0);
            
            isScrollMoveCompleted = false;
            // 这里需要根据具体的tableView类型来调用滚动方法
            tableView.scrollByItemIndex(scrollIndex, 1f, () => { isScrollMoveCompleted = true; });
            GameEntry.UI.OpenUIForm(EnumUIForm.UILockForm, 2f);
        }

        #endregion

        #region UI操作策略模式

        /// <summary>
        /// UI操作策略接口（合并了滚动和元素定位功能）
        /// </summary>
        public interface IUIActionStrategy
        {
            /// <summary>
            /// 执行滚动操作
            /// </summary>
            /// <param name="uiForm">UI表单对象</param>
            /// <param name="nodeName">节点名称</param>
            /// <param name="targetId">目标项目ID</param>
            /// <param name="component">GotoComponent实例</param>
            void ExecuteScroll(object uiForm, string nodeName, int targetId, GotoComponent component);

            /// <summary>
            /// 定位UI元素
            /// </summary>
            /// <param name="uiForm">UI表单对象</param>
            /// <param name="itemId">目标元素ID</param>
            /// <param name="component">GotoComponent实例</param>
            /// <returns>定位到的Transform，如果未找到返回null</returns>
            Transform LocateItem(object uiForm, int itemId, GotoComponent component);
        }

        /// <summary>
        /// UIMallForm UI操作策略
        /// </summary>
        public class UIMallFormActionStrategy : IUIActionStrategy
        {
            public void ExecuteScroll(object uiForm, string nodeName, int targetId, GotoComponent component)
            {
                if (uiForm is not UIMallForm targetForm) return;
                component.curGiftId = targetId;
                var switchPanelLogic = targetForm.GetSwitchPanelLogic((paymenttype)component.curShopId);
                var gifts = GameEntry.LogicData.MallData.GetGiftList((paymenttype)component.curShopId);
                int index = component.GetItemIndexInList(gifts, targetId, gift => gift.Id);
                Mosframe.TableView tableView = ReflectionHelper.GetMemberValue(switchPanelLogic, nodeName ?? "m_TableViewV") as Mosframe.TableView;
                if (tableView != null)
                {
                    component.ExecuteShopScroll(tableView, index, 1);
                }
            }

            public Transform LocateItem(object uiForm, int itemId, GotoComponent component)
            {
                if (uiForm is not UIMallForm targetForm) return null;
                var gifts = GameEntry.LogicData.MallData.GetGiftList((paymenttype)component.curShopId);
                int giftIndex = component.GetItemIndexInList(gifts, component.curGiftId, gift => gift.Id);
                var switchPanelLogic = targetForm.GetSwitchPanelLogic((paymenttype)component.curShopId);
                var weeklyDeal = switchPanelLogic as Mall_WeeklyDeal;
                if (weeklyDeal == null) return null;
                if (weeklyDeal.rewardRootDic.TryGetValue(giftIndex, out var root) == true && root != null)
                {
                    foreach (Transform item in root)
                    {
                        Transform node = item.Find("node/itemObj(Clone)");
                        if (node == null) continue;
                        UIItemModule uiItemModule = node.GetComponent<UIItemModule>();
                        if (uiItemModule != null && (itemid)itemId == uiItemModule.itemModule.ItemId)
                        {
                            return uiItemModule.transform;
                        }
                    }
                }
                return null;
            }
        }

        /// <summary>
        /// UIGeneralShopForm UI操作策略
        /// </summary>
        public class UIGeneralShopFormActionStrategy : IUIActionStrategy
        {
            public void ExecuteScroll(object uiForm, string nodeName, int targetId, GotoComponent component)
            {
                if (uiForm is not UIGeneralShopForm targetForm) return;

                component.curGiftId = targetId;
                var storetype = (storetype)component.curShopId;
                var switchPanelLogic = targetForm.GetSwitchPanelLogic(storetype);
                var goodsList = GameEntry.LogicData.GeneralShopData.GetGoodsListByType(storetype);
                int index = component.GetItemIndexInList(goodsList, targetId, goods => goods.Id);
                Mosframe.TableView tableView = ReflectionHelper.GetMemberValue(switchPanelLogic, nodeName ?? "m_TableViewV") as Mosframe.TableView;
                if (tableView != null)
                {
                    component.ExecuteShopScroll(tableView, index, 3);
                }
            }

            public Transform LocateItem(object uiForm, int itemId, GotoComponent component)
            {
                if (uiForm is not UIGeneralShopForm targetForm) return null;

                var switchPanelLogic = targetForm.GetSwitchPanelLogic((storetype)component.curShopId);
                GeneralShopLogic generalShopLogic = switchPanelLogic as GeneralShopLogic;
                if (generalShopLogic == null || generalShopLogic.goodsItemDic == null) return null;
                if (generalShopLogic.goodsItemDic.TryGetValue(component.curGoodsId, out var root) == true)
                {
                    return root;
                }
                return null;
            }
        }

        /// <summary>
        /// UI操作策略映射表
        /// </summary>
        private static readonly Dictionary<EnumUIForm, IUIActionStrategy> UIActionStrategies = new()
        {
            [EnumUIForm.UIMallForm] = new UIMallFormActionStrategy(),
            [EnumUIForm.UIGeneralShopForm] = new UIGeneralShopFormActionStrategy(),
        };

        /// <summary>
        /// 注册自定义UI操作策略
        /// </summary>
        /// <param name="uiForm">UI枚举</param>
        /// <param name="strategy">UI操作策略</param>
        public void RegisterUIActionStrategy(EnumUIForm uiForm, IUIActionStrategy strategy)
        {
            UIActionStrategies[uiForm] = strategy;
            ColorLog.Green($"注册UI操作策略: {uiForm}");
        }

        /// <summary>
        /// 执行通用UI滚动操作
        /// </summary>
        /// <param name="uiForm">UI表单枚举</param>
        /// <param name="nodeName">节点名称</param>
        /// <param name="targetId">目标项目ID</param>
        /// <returns>是否执行成功</returns>
        bool ExecuteUIScroll(EnumUIForm uiForm, string nodeName, int targetId)
        {
            if (!UIActionStrategies.TryGetValue(uiForm, out var strategy))
            {
                ColorLog.Yellow($"未找到 {uiForm} 的UI操作策略");
                return false;
            }

            var targetFormObject = GameEntry.UI.GetUIForm(uiForm)?.UIForm?.Logic;
            if (targetFormObject == null)
            {
                ColorLog.Red($"未找到 {uiForm} 的UI实例");
                return false;
            }

            try
            {
                strategy.ExecuteScroll(targetFormObject, nodeName, targetId, this);
                return true;
            }
            catch (Exception e)
            {
                ColorLog.Red($"执行 {uiForm} 滚动操作失败: {e.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行通用UI元素定位操作
        /// </summary>
        /// <param name="uiForm">UI表单枚举</param>
        /// <param name="itemId">目标元素ID</param>
        /// <returns>定位到的Transform，如果未找到返回null</returns>
        Transform ExecuteUIItemLocate(EnumUIForm uiForm, int itemId)
        {
            if (!UIActionStrategies.TryGetValue(uiForm, out var strategy))
            {
                ColorLog.Yellow($"未找到 {uiForm} 的UI操作策略");
                return null;
            }

            var targetFormObject = GameEntry.UI.GetUIForm(uiForm)?.UIForm?.Logic;
            if (targetFormObject == null)
            {
                ColorLog.Red($"未找到 {uiForm} 的UI实例");
                return null;
            }

            try
            {
                Transform result = strategy.LocateItem(targetFormObject, itemId, this);
                if (result != null)
                {
                    ColorLog.Green($"成功定位 {uiForm} 中的元素，ID: {itemId}");
                }
                else
                {
                    ColorLog.Yellow($"未在 {uiForm} 中找到元素，ID: {itemId}");
                }
                return result;
            }
            catch (Exception e)
            {
                ColorLog.Red($"执行 {uiForm} 元素定位操作失败: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// 通用反射工具类，用于动态获取TableView组件
        /// </summary>
        public static class ReflectionHelper
        {
            /// <summary>
            /// 通过反射动态获取对象的字段或属性值
            /// </summary>
            /// <param name="target">目标对象</param>
            /// <param name="memberName">字段或属性名称</param>
            /// <returns>字段或属性值，如果未找到返回null</returns>
            public static object GetMemberValue(object target, string memberName)
            {
                if (target == null || string.IsNullOrEmpty(memberName))
                    return null;

                Type targetType = target.GetType();

                try
                {
                    // 优先尝试获取字段
                    var field = targetType.GetField(memberName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    if (field != null)
                    {
                        return field.GetValue(target);
                    }

                    // 如果字段不存在，尝试获取属性
                    var property = targetType.GetProperty(memberName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                    if (property != null && property.CanRead)
                    {
                        return property.GetValue(target);
                    }

                    ColorLog.Yellow($"未找到字段或属性: {memberName} 在类型 {targetType.Name} 中");
                    return null;
                }
                catch (Exception ex)
                {
                    ColorLog.Red($"反射获取成员值失败: {memberName}, 错误: {ex.Message}");
                    return null;
                }
            }
        }

        #endregion

        #region 条件判断

        /// <summary>
        /// 条件判断
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleCondition(GotoChainData chainData)
        {
            string condition = chainData.condition;
            if (m_conditionHandlers.TryGetValue(condition, out var handler))
            {
                handler.Invoke(chainData);
            }
        }

        /// <summary>
        /// 查找建筑
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindBuilding(GotoChainData chainData)
        {
            uint buildingId = uint.Parse(chainData.param1);
            curBuildingId = buildingId;
            BuildingModule buildingModule = GetBuildingModule(buildingId);
            SetConditionResult(buildingModule != null, chainData);
        }

        /// <summary>
        /// 建筑状态
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionBuildingState(GotoChainData chainData)
        {
            string state = chainData.param1;
            BuildingModule buildingModule = GetBuildingModule(curBuildingId);
            bool conditionMet = false;
            if (buildingModule != null)
            {
                switch (state)
                {
                    case "normal":
                        conditionMet = buildingModule.GetBuildingState() == BuildingState.Normal;
                        break;
                    case "broken":
                        conditionMet = buildingModule.LEVEL == 0;
                        break;
                    case "reward":
                        conditionMet = buildingModule.GetBuildingState() == BuildingState.ConstructionComplete || 
                                     buildingModule.GetBuildingState() == BuildingState.UpgradeComplete;
                        break;
                }
            }
            SetConditionResult(conditionMet, chainData);
        }

        /// <summary>
        /// 是否存在于建筑列表
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionBuildingListExist(GotoChainData chainData)
        {
            uint buildingId = uint.Parse(chainData.param1);
            curBuildingId = buildingId;
            bool canShow = GameEntry.LogicData.BuildingData.CheckBuildingCanShow((int)curBuildingId);
            SetConditionResult(canShow, chainData);
        }

        /// <summary>
        /// 商城页签是否解锁
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindShoppingCenter(GotoChainData chainData)
        {
            int id = int.Parse(chainData.param1);
            bool isMallUnlock = GameEntry.LogicData.MallData.IsMallUnlock();
            // 商城入口
            if (id % 100 == 0)
            {
                SetConditionResult(isMallUnlock, chainData);
                return;
            }
            // 商城页签
            bool isShopUnlock = GameEntry.LogicData.MallData.IsUnlockCheckByServer(id);
            curShopId = id;
            SetConditionResult(isMallUnlock && isShopUnlock, chainData);
        }

        /// <summary>
        /// 礼包是否售罄
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindGiftPack(GotoChainData chainData)
        {
            List<Gift.Gift> gifts = GameEntry.LogicData.MallData.GetGiftList((paymenttype)curShopId);
            int giftId = int.Parse(chainData.param1);
            bool hasAvailableGift = false;
            foreach (var item in gifts)
            {
                if (item.Id == giftId && (item.MaxAmount - item.Amount) > 0)
                {
                    hasAvailableGift = true;
                    break;
                }
            }
            SetConditionResult(hasAvailableGift, chainData);
        }

        /// <summary>
        /// 商店是否开启
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindStore(GotoChainData chainData)
        {
            int shopId = int.Parse(chainData.param1);
            curShopId = shopId;
            bool isStoreOpen = CheckStoreOpen(shopId);
            SetConditionResult(isStoreOpen, chainData);
        }

        /// <summary>
        /// 检查商店是否开启
        /// </summary>
        /// <param name="shopId">商店ID</param>
        /// <returns>是否开启</returns>
        bool CheckStoreOpen(int shopId)
        {
            var mainConfig = GameEntry.LDLTable.GetTable<store_type>();
            for (var i = 0; i < mainConfig.Count; i++)
            {
                var node = mainConfig[i];
                if (node.id != (storetype)shopId) continue;
                var isUnlock = ToolScriptExtend.CheckDemandUnlockList(node.unlock_demand);
                if (isUnlock)
                {
                    if (node.id == storetype.storetype_alliance)
                    {
                        var isJoin = GameEntry.LogicData.UnionData.IsJoinUnion();
                        if (!isJoin) continue;
                    }
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 商品是否售罄
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindGoods(GotoChainData chainData)
        {
            int goodsId = int.Parse(chainData.param1);
            curGoodsId = goodsId;
            bool isSoldOut = CheckGoodsSoldOut(goodsId);
            SetConditionResult(!isSoldOut, chainData);
        }

        /// <summary>
        /// 检查商品是否售罄
        /// </summary>
        /// <param name="goodsId">商品ID</param>
        /// <returns>是否售罄</returns>
        bool CheckGoodsSoldOut(int goodsId)
        {
            var goodsList = GameEntry.LogicData.GeneralShopData.GetGoodsListByType((storetype)curShopId);
            foreach (var item in goodsList)
            {
                if (item.Id == goodsId)
                {
                    var goodsConfig = GameEntry.LogicData.GeneralShopData.GetGoodsConfigById(goodsId);
                    if (goodsConfig == null) continue;
                    var maxCount = goodsConfig.purchase_number;
                    if (maxCount == 0)
                    {
                        return false;
                    }
                    else
                    {
                        var boughtCount = GameEntry.LogicData.GeneralShopData.GetBoughtCount((storetype)curShopId, goodsId);
                        return boughtCount >= maxCount;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 判断场景
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionJudgeScene(GotoChainData chainData)
        {
            string sceneName = chainData.param1;
            Scene activeScene = SceneManager.GetActiveScene();
            bool isCurrentScene = activeScene.name == sceneName;
            SetConditionResult(isCurrentScene, chainData);
        }

        /// <summary>
        /// 查找内城地块
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleConditionFindInnerCityPath(GotoChainData chainData)
        {
            int pvePathId = int.Parse(chainData.param1);
            curPvePathId = pvePathId;

            // 通过 PvePathData 查找指定 ID 的内城地块
            curPvePathModule = GameEntry.LogicData.PvePathData.GetPvePathModuleById(pvePathId);
            SetConditionResult(curPvePathModule != null, chainData);
        }

        #endregion

        #region UI跳转

        /// <summary>
        /// 飘字
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleFloatingFont(GotoChainData chainData)
        {
            int langId = int.Parse(chainData.param1);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIFlyTextForm, new FlyTextParams()
            {
                Content = ToolScriptExtend.GetLang(langId)
            });
            curNext = chainData.next;
        }

        /// <summary>
        /// 跳转到指定 UI 页面
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleMoveToPage(GotoChainData chainData)
        {
            string param1 = chainData.param1;
            string param2 = chainData.param2;
            EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), param1);
            curEnumUIForm = enumUIForm;
            GameEntry.UI.OpenUIForm(enumUIForm, int.Parse(param2));
            GameEntry.UI.CloseUIForm(EnumUIForm.UIGetWayForm);
            curNext = chainData.next;
        }

        /// <summary>
        /// 通用滚动列表处理方法（优化版）
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleSlideListAsync(GotoChainData chainData)
        {
            string param1 = chainData.param1;
            string param2 = chainData.param2;

            // 解析目标ID
            if (!int.TryParse(param2, out int targetId))
            {
                ColorLog.Red($"参数解析失败，无效的目标ID: {param2}");
                curNext = chainData.next;
                yield break;
            }

            // 等待UI加载完成（通用方法）
            yield return StartCoroutine(WaitForUIFormLoadedAsync(curEnumUIForm, 5.0f));

            var result = lastWaitResult;
            if (result == null || !result.success)
            {
                string errorMsg = result?.errorMessage ?? "未知错误";
                ColorLog.Red($"UI {curEnumUIForm} 加载失败: {errorMsg}");
                curNext = chainData.next;
                yield break;
            }

            // 使用策略模式执行滚动操作
            bool scrollSuccess = ExecuteUIScroll(curEnumUIForm, param1, targetId);
            if (!scrollSuccess)
            {
                ColorLog.Yellow($"滚动操作失败，UI: {curEnumUIForm}, 目标ID: {targetId}");
            }

            curNext = chainData.next;
        }

        /// <summary>
        /// 通用UI元素定位处理方法（优化版）
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleUILocateItemAsync(GotoChainData chainData)
        {
            string param1 = chainData.param1;
            string param2 = chainData.param2;

            // 解析目标元素ID
            if (!int.TryParse(param1, out int itemId))
            {
                ColorLog.Red($"参数解析失败，无效的元素ID: {param1}");
                curNext = chainData.next;
                yield break;
            }

            // 等待滚动完成
            bool conditionFunc() { return isScrollMoveCompleted; }
            yield return StartCoroutine(WaitForConditionAsync(conditionFunc));

            // 使用策略模式执行元素定位操作
            Transform locatedTransform = ExecuteUIItemLocate(curEnumUIForm, itemId);
            
            // 更新当前定位的元素Transform
            curItemTransform = locatedTransform;

            curNext = chainData.next;
        }

        /// <summary>
        /// 清理主界面上方的所有 UI
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleCleanUIAll(GotoChainData chainData)
        {
            List<UIForm> uiForms = GameEntry.UI.GetAllLoadedUIFormsWithSortAndIgnore();
            for (int i = uiForms.Count - 1; i >= 0; i--)
            {
                // 界面资源路径
                string assetName = uiForms[i].UIFormAssetName;
                EnumUIForm enumUIForm = GameEntry.UI.GetUIFormEnumByAssetName(assetName);
                if (enumUIForm == EnumUIForm.UIMainFaceForm) continue;
                GameEntry.UI.CloseUIForm(uiForms[i]);
            }
            curNext = chainData.next;
        }

        /// <summary>
        /// UI 界面定位节点
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleUILocateNodeAsync(GotoChainData chainData)
        {
            string param1 = chainData.param1;
            string param2 = chainData.param2;

            if (param1.Contains("m_img"))
            {
                UIMainFaceForm mainFaceForm = GameEntry.UI.GetUIForm(EnumUIForm.UIMainFaceForm) as UIMainFaceForm;
                Image img = ReflectionHelper.GetMemberValue(mainFaceForm, param1 ?? "m_imgHeroEntry") as Image;
                if (img != null)
                {
                    curItemTransform = img.transform;
                    isLocateMainface = true;
                }
            }
            else if (param1.Contains("m_btn"))
            {
                UIMainFaceForm mainFaceForm = GameEntry.UI.GetUIForm(EnumUIForm.UIMainFaceForm) as UIMainFaceForm;
                Button btn = ReflectionHelper.GetMemberValue(mainFaceForm, param1 ?? "m_btnHero") as Button;
                if (btn != null)
                {
                    curItemTransform = btn.transform;
                    isLocateMainface = true;
                }
            }

            yield return null;
            curNext = chainData.next;
        }

        /// <summary>
        /// 替换UI界面
        /// 参数: [要关闭的UI名称, 要打开的UI名称, UI组ID(可选)]
        /// </summary>
        void HandleReplaceUI(GotoChainData chainData)
        {
            // if (parameters.Length < 2)
            // {
            //     ColorLog.Red("replace_ui 参数不足，需要关闭UI名称和打开UI名称");
            //     return;
            // }

            // string closeUIName = parameters[0];
            // string openUIName = parameters[1];
            // string uiGroupName = parameters.Length > 2 ? parameters[2] : "Default";

            // ColorLog.Green($"替换UI: 关闭 {closeUIName}, 打开 {openUIName}");

            // EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), closeUIName);
            // GameEntry.UI.CloseUIForm(enumUIForm);
            // GameEntry.UI.OpenUIForm(openUIName, uiGroupName);
        }

        #endregion

        #region 建筑跳转

        /// <summary>
        /// 定位建筑
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleSceneLocateBuilding(GotoChainData chainData)
        {
            uint buildingId = uint.Parse(chainData.param1);
            curBuildingId = buildingId;
            BuildingModule buildingModule = GetBuildingModule(buildingId);
            if (buildingModule != null)
            {
                curNext = chainData.next;
                curBuildingPos = buildingModule.GetWorldCenterPosition();
            }
        }

        /// <summary>
        /// 模拟点击建筑
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleSimulateClick(GotoChainData chainData)
        {
            uint buildingId = uint.Parse(chainData.param1);
            BuildingModule buildingModule = GetBuildingModule(buildingId);
            if (buildingModule != null)
            {
                Entity entity = GameEntry.Entity.GetGameEntity(buildingModule.UID);
                if (entity != null)
                {
                    entity.OnClick();
                }
                curNext = chainData.next;
            }
        }

        /// <summary>
        /// 定位建筑菜单按钮
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleUILocateButtonAsync(GotoChainData chainData)
        {
            int chainId = chainData.id;
            go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(chainId);
            string param = chainData.param1;

            // 使用通用的事件等待方法
            yield return StartCoroutine(WaitForUIFormLoadedAsync(
                EnumUIForm.UIBuildingMenuForm,
                5.0f // 超时时间5秒
            ));
            
            // 获取等待结果
            var result = lastWaitResult;

            // 处理结果
            if (result != null && result.success && result.targetObject is UIBuildingMenuForm targetForm)
            {
                int menuId = int.Parse(param);
                curMenuId = menuId;
                ColorLog.Green($"UI定位按钮完成，menuId: {menuId}");
            }
            else
            {
                string errorMsg = result?.errorMessage ?? "未知错误";
                ColorLog.Red($"UIBuildingMenuForm 加载失败: {errorMsg}");
            }

            curNext = chainConfig.next;
        }

        /// <summary>
        /// 打开建筑列表
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleOpenBuildingList(GotoChainData chainData)
        {
            uint buildingId = uint.Parse(chainData.param1);
            BuildingModule buildingModule = GetBuildingModule(buildingId) ?? BuildingModule.Create((int)buildingId, 1, 1);
            GameEntry.UI.OpenUIForm(EnumUIForm.UIMainFaceBuildForm, buildingModule.buildingCfg);
            curNext = chainData.next;
        }

        #endregion

        #region 场景跳转

        /// <summary>
        /// 异步切换场景
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleMoveToSceneAsync(GotoChainData chainData)
        {
            int chainId = chainData.id;
            go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(chainId);
            string condition = chainData.condition;
            string param = chainData.param1;
            string targetSceneName = string.Empty;

            // 切换到内城场景
            if (param == "MainScene")
            {
                var tCurrentProcedure = Game.GameEntry.Procedure.CurrentProcedure;
                if (tCurrentProcedure is ProcedureWorldMap procedureWorldMap)
                {
                    procedureWorldMap.GotoMainCity();
                    targetSceneName = $"Assets/ResPackage/Scenes/{param}.unity";
                }
                else if (tCurrentProcedure is Procedure5v5Battle procedure5v5Battle)
                {
                    procedure5v5Battle.GotoMainCity();
                    targetSceneName = $"Assets/ResPackage/Scenes/{param}.unity";
                }
                curNext = chainConfig.next;
            }

            if (targetSceneName.Equals(string.Empty))
            {
                CompleteStep(GotoExecuteResult.Success);
                yield break;
            }

            // 等待场景加载完成
            yield return StartCoroutine(WaitForGameEventAsync<LoadSceneSuccessEventArgs>
            (
                LoadSceneSuccessEventArgs.EventId,
                eventArgs => eventArgs.SceneAssetName == targetSceneName
            ));
        }

        /// <summary>
        /// 重新加载当前场景
        /// </summary>
        void HandleReloadScene(GotoChainData chainData)
        {
            ColorLog.Green("重新加载当前场景");

            // 获取当前场景名称并重新加载
            string currentSceneName = UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
            GameEntry.Scene.LoadScene(currentSceneName);
        }

        #endregion

        #region 相机跳转

        /// <summary>
        /// 移动相机
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        IEnumerator HandleMoveCameraAsync(GotoChainData chainData)
        {
            int chainId = chainData.id;
            string condition = chainData.condition;
            string param = chainData.param1;

            bool isMoveCompleted = false;
            float duration = 0.5f;

            Vector3 targetPos = Vector3.zero;
            if (curBuildingPos != Vector3.zero)
            {
                targetPos = curBuildingPos;
            }
            else if (curPvePathPos != Vector3.zero)
            {
                targetPos = curPvePathPos;
            }

            Vector3 curCameraPos = GameEntry.Camera.GetCurrentLookAtPosition();
            if (Mathf.Approximately(curCameraPos.x, targetPos.x)
            &&  Mathf.Approximately(curCameraPos.y, targetPos.y)
            &&  Mathf.Approximately(curCameraPos.z, targetPos.z))
            {
                duration = 0f;
            }

            GameEntry.Camera.LookAtPosition(targetPos, () =>
            {
                isMoveCompleted = true;
            }, duration);

            bool conditionFunc() { return isMoveCompleted; }
            yield return StartCoroutine(WaitForConditionAsync(conditionFunc));
            
            curNext = chainData.next;
        }

        #endregion

        #region 动画跳转

        /// <summary>
        /// 播放动画
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandlePlayAnimation(GotoChainData chainData)
        {
            string param = chainData.param1;
            if (param == "UIArrow")
            {
                UIBuildingMenuForm uiBuildingMenuForm = GameEntry.UI.GetUIForm(EnumUIForm.UIBuildingMenuForm) as UIBuildingMenuForm;
                if (uiBuildingMenuForm != null)
                {
                    uiBuildingMenuForm.ShowBtnArrow(curMenuId);
                }

                UIMallForm uiMallForm = GameEntry.UI.GetUIForm(EnumUIForm.UIMallForm) as UIMallForm;
                if (uiMallForm != null)
                {
                    UIClickEffectForm uiClickEffectForm = GameEntry.UI.GetUIForm(EnumUIForm.UIClickEffectForm) as UIClickEffectForm;
                    if (uiClickEffectForm != null && curItemTransform != null)
                    {
                        Vector3 screenPosition = GameEntry.Camera.UICamera.WorldToScreenPoint(curItemTransform.position);
                        uiClickEffectForm.ShowAnim(screenPosition, "UIArrow", new Vector2(0, 50));
                    }
                }

                UIGeneralShopForm uiGeneralShopForm = GameEntry.UI.GetUIForm(EnumUIForm.UIGeneralShopForm) as UIGeneralShopForm;
                if (uiGeneralShopForm != null)
                {
                    UIClickEffectForm uiClickEffectForm = GameEntry.UI.GetUIForm(EnumUIForm.UIClickEffectForm) as UIClickEffectForm;
                    if (uiClickEffectForm != null && curItemTransform != null)
                    {
                        Vector3 screenPosition = GameEntry.Camera.UICamera.WorldToScreenPoint(curItemTransform.position);
                        uiClickEffectForm.ShowAnim(screenPosition, "battle_zhucheng_dianjishou");
                    }
                }

                if (isLocateMainface)
                {
                    UIMainFaceForm uiMainFaceForm = GameEntry.UI.GetUIForm(EnumUIForm.UIMainFaceForm) as UIMainFaceForm;
                    if (uiMainFaceForm != null)
                    {
                        UIClickEffectForm uiClickEffectForm = GameEntry.UI.GetUIForm(EnumUIForm.UIClickEffectForm) as UIClickEffectForm;
                        if (uiClickEffectForm != null && curItemTransform != null)
                        {
                            Vector3 screenPosition = GameEntry.Camera.UICamera.WorldToScreenPoint(curItemTransform.position);
                            uiClickEffectForm.ShowAnim(screenPosition, "UIArrow", new Vector2(0, -30));
                        }
                    }
                }

                if (curPvePathId != 0)
                {
                    
                }
            }
            curNext = chainData.next;
        }

        #endregion

        #region 内城地块跳转

        /// <summary>
        /// 定位地块坐标
        /// </summary>
        /// <param name="chainData">跳转链数据</param>
        void HandleSceneLocateInnerCityPath(GotoChainData chainData)
        {
            int pvePathId = int.Parse(chainData.param1);
            curPvePathId = pvePathId;

            // 通过 PvePathData 查找指定 ID 的内城地块
            curPvePathModule = GameEntry.LogicData.PvePathData.GetPvePathModuleById(pvePathId);
            if (curPvePathModule != null)
            {
                curPvePathPos = curPvePathModule.GetPosition();
            }
            curNext = chainData.next;
        }

        #endregion

        #region 功能跳转

        /// <summary>
        /// 打开功能模块
        /// 参数: [功能名称, 功能参数(可选)]
        /// </summary>
        void HandleOpenFunction(GotoChainData chainData)
        {
            // if (parameters.Length < 1)
            // {
            //     ColorLog.Red("open_function 参数不足，需要功能名称");
            //     return;
            // }

            // string functionName = parameters[0];
            // string functionParam = parameters.Length > 1 ? parameters[1] : "";

            // ColorLog.Green($"打开功能: {functionName}, 参数: {functionParam}");

            // // 根据功能名称执行对应逻辑
            // switch (functionName.ToLower())
            // {
            //     case "shop":
            //         OpenShop(functionParam);
            //         break;
            //     case "inventory":
            //         OpenInventory(functionParam);
            //         break;
            //     case "settings":
            //         OpenSettings(functionParam);
            //         break;
            //     default:
            //         ColorLog.Red($"未知功能: {functionName}");
            //         break;
            // }
        }

        /// <summary>
        /// 关闭功能模块
        /// 参数: [功能名称]
        /// </summary>
        void HandleCloseFunction(GotoChainData chainData)
        {
            // if (parameters.Length < 1)
            // {
            //     ColorLog.Red("close_function 参数不足，需要功能名称");
            //     return;
            // }

            // string functionName = parameters[0];
            // ColorLog.Green($"关闭功能: {functionName}");
            // EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), functionName);

            // // 根据功能名称关闭对应UI
            // switch (functionName.ToLower())
            // {
            //     case "shop":
            //         GameEntry.UI.CloseUIForm(enumUIForm);
            //         break;
            //     case "inventory":
            //         GameEntry.UI.CloseUIForm(enumUIForm);
            //         break;
            //     case "settings":
            //         GameEntry.UI.CloseUIForm(enumUIForm);
            //         break;
            //     default:
            //         ColorLog.Red($"未知功能: {functionName}");
            //         break;
            // }
        }

        #endregion

        #region 游戏流程跳转

        /// <summary>
        /// 跳转到指定流程
        /// 参数: [流程名称]
        /// </summary>
        void HandleGotoProcedure(GotoChainData chainData)
        {
            // if (parameters.Length < 1)
            // {
            //     ColorLog.Red("goto_procedure 参数不足，需要流程名称");
            //     return;
            // }

            // string procedureName = parameters[0];
            // ColorLog.Green($"跳转到流程: {procedureName}");

            // 使用GameFramework的流程系统
            // GameEntry.Procedure.StartProcedure(procedureName);
        }

        #endregion
    }
}
