# Shader Graph Asset

## Description

The **Shader Graph Asset** is the new **Asset** type introduced with the shader graph. You can create a **Shader Graph Asset** from the [Project Window](https://docs.unity3d.com/Manual/ProjectView.html) from the **Create** menu.

For convenience there is a **Create** menu entry for **Blank Shader Graph** and [Sub-graph](Sub-graph.md). They can be found in the **Shader** sub-menu. Additional options may be provided by render pipelines. These options will create a new Shader Graph with required settings and [Block]() nodes in the [Master Stack]() for the selected shading model.

You can open the [Shader Graph Window](Shader-Graph-Window.md) by double clicking a **Shader Graph Asset** or by clicking **Open Shader Editor** in the [Inspector](https://docs.unity3d.com/Manual/UsingTheInspector.html) when the **Shader Graph Asset** is selected.
