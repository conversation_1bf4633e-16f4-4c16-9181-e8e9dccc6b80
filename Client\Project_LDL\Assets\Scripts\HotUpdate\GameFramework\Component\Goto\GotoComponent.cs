using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityGameFramework.Runtime;
using UnityEngine.Events;
using Game.Hotfix.Config;
using GameFramework.Event;

namespace Game.Hotfix
{
    #region 数据结构定义

    /// <summary>
    /// 跳转执行结果
    /// </summary>
    public enum GotoExecuteResult
    {
        Success,        // 执行成功
        Failed,         // 执行失败
        Async           // 异步执行中
    }

    /// <summary>
    /// 跳转执行上下文
    /// </summary>
    public class GotoExecuteContext
    {
        public go_to config;                           // 当前配置
        public HashSet<int> executedIds = new();       // 已执行的 id 列表（防止循环）
        public float delayTime = 0f;                   // 延迟时间
        public Action<GotoExecuteResult> onComplete;   // 完成回调
    }

    /// <summary>
    /// 跳转链数据
    /// </summary>
    public class GotoChainData
    {
        public int id;               // 跳转链 id
        public string stepType;      // 步骤类型
        public string condition;     // 判断条件
        public int next;             // 下一步
        public int elseNext;         // 判断条件为 false 时的下一步
        public string param1;        // 参数 1
        public string param2;        // 参数 2
        public string param3;        // 参数 3
    }

    #endregion

    [DisallowMultipleComponent]
    [AddComponentMenu("GameCustom/GotoComponent")]
    public partial class GotoComponent : GameFrameworkComponent
    {
        #region 成员变量

        Dictionary<string, Action<GotoChainData>> m_keyActionDic;                 // 同步动作字典
        Dictionary<string, Func<GotoChainData, IEnumerator>> m_asyncActionDic;    // 异步动作字典
        Dictionary<string, Action<GotoChainData>> m_conditionHandlers;            // 条件判断字典
        readonly Dictionary<int, GotoChainData> m_chainDataDic = new();           // 跳转数据字典

        int curNext = -1;           // 当前执行的跳转链 id
        uint curBuildingId;         // 当前建筑 id
        int curMenuId;              // 当前建筑菜单 id
        Vector3 curBuildingPos;     // 当前建筑位置
        int curShopId;              // 当前商店 id
        EnumUIForm curEnumUIForm;   // 当前打开的界面
        int curGiftId;              // 当前礼包 id
        int curGoodsId;             // 当前商品 id
        bool isScrollMoveCompleted; // 滑动是否完成
        Transform curItemTransform; // 当前定位的图标
        bool isLocateMainface;      // 是否定位主界面的图标
        int curPathId;              // 当前地块 id

        private bool stepCompleted = false;                                  // 步骤是否完成
        private GotoExecuteResult stepResult = GotoExecuteResult.Success;    // 步骤执行结果
        private UnityEvent onStepCompleted = new();                          // 步骤执行回调
        private Coroutine currentChainCoroutine;                             // 当前执行的跳转链协程

        #endregion

        #region 初始化

        void Start()
        {
            RegisterAction();
            RegisterCondition();
        }

        /// <summary>
        /// 注册跳转动作
        /// </summary>
        void RegisterAction()
        {
            // 注册同步跳转
            m_keyActionDic = new Dictionary<string, Action<GotoChainData>>
            {
                ["if"] = HandleCondition,                                     // 条件判断
                ["reload_scene"] = HandleReloadScene,                         // 切换场景
                ["scene_locate_building"] = HandleSceneLocateBuilding,        // 场景定位建筑
                ["simulate_click"] = HandleSimulateClick,                     // 模拟点击
                ["open_building_list"] = HandleOpenBuildingList,              // 打开建筑列表
                ["play_animation"] = HandlePlayAnimation,                     // 播放动画
                ["move_to_page"] = HandleMoveToPage,                          // 跳转到指定 UI 页面
                ["replace_ui"] = HandleReplaceUI,                             // 替换UI界面
                ["floating_font"] = HandleFloatingFont,                       // 飘字
                ["clean_ui_all"] = HandleCleanUIAll,                          // 清理所有UI
                ["open_function"] = HandleOpenFunction,                       // 打开功能模块
                ["close_function"] = HandleCloseFunction,                     // 关闭功能模块
                ["goto_procedure"] = HandleGotoProcedure,                     // 跳转到指定游戏流程
                ["delay"] = HandleDelay,                                      // 延迟
                ["wait_ui_close"] = HandleWaitUIClose,                        // 等待UI关闭
                ["wait_condition"] = HandleWaitCondition,                     // 等待条件
            };

            // 注册异步跳转
            m_asyncActionDic = new Dictionary<string, Func<GotoChainData, IEnumerator>>
            {
                ["move_to_scene"] = HandleMoveToSceneAsync,                   // 场景跳转
                ["ui_locate_button"] = HandleUILocateButtonAsync,             // 定位建筑菜单按钮
                ["move_camera"] = HandleMoveCameraAsync,                      // 移动相机
                ["slide_list"] = HandleSlideListAsync,                        // 滚动列表
                ["ui_locate_item"] = HandleUILocateItemAsync,                 // 定位按钮
                ["ui_locate_node"] = HandleUILocateNodeAsync,                 // 定位节点
                ["show_ui_async"] = HandleShowUIAsync,                        // 异步显示UI
                ["load_scene_async"] = HandleLoadSceneAsync,                  // 异步加载场景
                ["wait_seconds"] = HandleWaitSeconds,                         // 等待秒数
                ["wait_until_event"] = HandleWaitUntilEvent,                  // 等待事件
                ["wait_ui_close_async"] = HandleWaitUICloseAsync,             // 等待UI关闭
                ["wait_custom_condition"] = HandleWaitCustomConditionAsync,   // 等待自定义条件
            };
        }

        /// <summary>
        /// 注册条件判断
        /// </summary>
        void RegisterCondition()
        {
            m_conditionHandlers = new Dictionary<string, Action<GotoChainData>>
            {
                ["find_building"] = HandleConditionFindBuilding,              // 查找建筑
                ["building_state"] = HandleConditionBuildingState,            // 建筑状态
                ["building_list_exist"] = HandleConditionBuildingListExist,   // 建筑列表是否存在
                ["find_shopping_center"] = HandleConditionFindShoppingCenter, // 查找商城
                ["find_gift_pack"] = HandleConditionFindGiftPack,             // 查找礼包
                ["find_store"] = HandleConditionFindStore,                    // 查找商店
                ["find_goods"] = HandleConditionFindGoods,                    // 查找商品
                ["judge_scene"] = HandleConditionJudgeScene,                  // 判断场景
                ["find_innercity_path"] = HandleConditionFindInnerCityPath,   // 查找内城地块
            };
        }

        #endregion

        #region 执行跳转

        /// <summary>
        /// 根据配置表 id 执行跳转（支持链式跳转）
        /// </summary>
        /// <param name="id">配置表 id</param>
        /// <param name="onComplete">完成回调</param>
        public void Go(int id, Action<GotoExecuteResult> onComplete = null)
        {
            ResetParam();
            go_to config = GameEntry.LDLTable.GetTableById<go_to>(id);
            if (config == null)
            {
                ColorLog.Red("go_to 表找不到 id = ", id);
                onComplete?.Invoke(GotoExecuteResult.Failed);
                return;
            }

            var context = new GotoExecuteContext
            {
                config = config,
                onComplete = onComplete
            };

            // 填充跳转链数据（可能缺少某个步骤的参数，执行时会做检查）
            m_chainDataDic.Clear();
            foreach (var item in config.goto_param)
            {
                go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(item.go_to_chain_name);

                // 已有参数 1，继续填充参数 2 和 3
                if (m_chainDataDic.ContainsKey(item.go_to_chain_name))
                {
                    if (string.IsNullOrEmpty(m_chainDataDic[item.go_to_chain_name].param2))
                    {
                        m_chainDataDic[item.go_to_chain_name].param2 = item.value;
                    }
                    else if (string.IsNullOrEmpty(m_chainDataDic[item.go_to_chain_name].param3))
                    {
                        m_chainDataDic[item.go_to_chain_name].param3 = item.value;
                    }
                }
                else
                {
                    m_chainDataDic.Add(item.go_to_chain_name, new GotoChainData
                    {
                        id = item.go_to_chain_name,
                        stepType = chainConfig?.step_type,
                        condition = chainConfig?.conditions,
                        next = chainConfig != null ? chainConfig.next : -1,
                        elseNext = chainConfig != null ? chainConfig.else_next : -1,
                        param1 = item.value
                    });
                }
            }

            // 停止之前的协程（如果有的话）
            if (currentChainCoroutine != null)
            {
                StopCoroutine(currentChainCoroutine);
            }

            // 启动新的协程链
            currentChainCoroutine = StartCoroutine(ExecuteGotoChainCoroutine(context));
        }

        /// <summary>
        /// 协程形式执行跳转链
        /// </summary>
        /// <param name="context">执行上下文</param>
        IEnumerator ExecuteGotoChainCoroutine(GotoExecuteContext context)
        {
            if (context.config == null)
            {
                ColorLog.Yellow("跳转配置为空");
                context.onComplete?.Invoke(GotoExecuteResult.Failed);
                yield break;
            }

            // 设置初始状态
            curNext = context.config.logical_chain_first_ID;

            while (curNext != -1)
            {
                // 防止无限循环
                if (context.executedIds.Contains(curNext))
                {
                    ColorLog.Yellow($"检测到循环跳转 停止执行 id = {curNext}");
                    context.onComplete?.Invoke(GotoExecuteResult.Failed);
                    yield break;
                }

                context.executedIds.Add(curNext);

                // 执行当前步骤
                stepCompleted = false;
                stepResult = GotoExecuteResult.Success;

                // 执行步骤并获取结果
                var result = ExecuteGotoStep(context.config);

                if (result == GotoExecuteResult.Failed)
                {
                    context.onComplete?.Invoke(GotoExecuteResult.Failed);
                    yield break;
                }

                // 如果是异步操作，等待步骤完成
                if (result == GotoExecuteResult.Async)
                {
                    // 使用 WaitUntil 等待异步操作完成
                    yield return new WaitUntil(() => stepCompleted);
                    
                    // 检查异步操作的结果
                    if (stepResult == GotoExecuteResult.Failed)
                    {
                        context.onComplete?.Invoke(GotoExecuteResult.Failed);
                        yield break;
                    }
                }

                // 处理延迟
                if (context.delayTime > 0)
                {
                    yield return new WaitForSeconds(context.delayTime);
                    context.delayTime = 0; // 重置延迟时间
                }

                if (curNext == -1)
                {
                    // 链式跳转完成
                    ColorLog.Green($"跳转链执行完成。总共执行了 {context.executedIds.Count} 个跳转");
                    context.onComplete?.Invoke(GotoExecuteResult.Success);
                    yield break;
                }
            }

            // 正常完成
            context.onComplete?.Invoke(GotoExecuteResult.Success);
        }

        /// <summary>
        /// 执行单个跳转步骤
        /// </summary>
        GotoExecuteResult ExecuteGotoStep(go_to config)
        {
            if (config == null)
            {
                ColorLog.Yellow("跳转配置为空");
                return GotoExecuteResult.Failed;
            }

            int chainId = curNext;
            if (chainId == 0) return GotoExecuteResult.Failed;

            // 跳转链数据不存在，构造填充
            if (!m_chainDataDic.ContainsKey(chainId))
            {
                go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(chainId);

                m_chainDataDic.Add(chainId, new GotoChainData
                {
                    id = chainId,
                    stepType = chainConfig?.step_type,
                    condition = chainConfig?.conditions,
                    next = chainConfig != null ? chainConfig.next : -1,
                    elseNext = chainConfig != null ? chainConfig.else_next : -1,
                    param1 = string.Empty
                });
            }

            GotoChainData gotoChainData = m_chainDataDic[chainId];
            string key = gotoChainData.stepType;

            ColorLog.Green($"执行跳转: id = {config.id}, stepType = {key}, condition = {gotoChainData.condition} parameters = {gotoChainData.param1}, {gotoChainData.param2}, {gotoChainData.param3}");

            try
            {
                // 优先检查异步动作
                if (m_asyncActionDic.TryGetValue(key, out Func<GotoChainData, IEnumerator> asyncAction))
                {
                    // 启动异步协程
                    StartCoroutine(ExecuteAsyncAction(asyncAction, gotoChainData));
                    return GotoExecuteResult.Async;
                }

                // 执行同步动作
                if (m_keyActionDic.TryGetValue(key, out Action<GotoChainData> action))
                {
                    action.Invoke(gotoChainData);
                    return GotoExecuteResult.Success;
                }

                ColorLog.Yellow($"未找到跳转动作处理器: {key}");
                return GotoExecuteResult.Failed;
            }
            catch (Exception e)
            {
                ColorLog.Yellow($"执行跳转动作失败: {key}, 错误: {e.Message}");
                return GotoExecuteResult.Failed;
            }
        }

        /// <summary>
        /// 执行异步动作的协程包装器
        /// </summary>
        IEnumerator ExecuteAsyncAction(Func<GotoChainData, IEnumerator> asyncAction, GotoChainData parameters)
        {
            bool success = true;
            IEnumerator routine = null;

            try
            {
                routine = asyncAction.Invoke(parameters);
            }
            catch (Exception e)
            {
                ColorLog.Red($"执行异步动作失败: {e.Message}");
                success = false;
            }

            if (success && routine != null)
            {
                yield return StartCoroutine(routine);
                CompleteStep(GotoExecuteResult.Success);
            }
            else
            {
                CompleteStep(GotoExecuteResult.Failed);
            }
        }

        /// <summary>
        /// 完成当前步骤（用于异步操作）
        /// </summary>
        /// <param name="result">执行结果</param>
        public void CompleteStep(GotoExecuteResult result = GotoExecuteResult.Success)
        {
            stepResult = result;
            stepCompleted = true;
            onStepCompleted.Invoke();
        }

        /// <summary>
        /// 停止当前协程链
        /// </summary>
        public void StopChain()
        {
            if (currentChainCoroutine != null)
            {
                StopCoroutine(currentChainCoroutine);
                currentChainCoroutine = null;
            }
            stepCompleted = false;
        }

        void ResetParam()
        {
            curNext = -1;
            curBuildingId = 0;
            curMenuId = 0;
            curBuildingPos = Vector3.zero;
            curShopId = 0;
            curEnumUIForm = EnumUIForm.None;
            curGiftId = 0;
            curGoodsId = 0;
            isScrollMoveCompleted = false;
            curItemTransform = null;
            isLocateMainface = false;
        }

        #endregion

        #region 链式跳转辅助方法

        /// <summary>
        /// 评估跳转条件
        /// 这里可以根据需要实现复杂的条件判断逻辑
        /// </summary>
        bool EvaluateCondition(string condition)
        {
            if (string.IsNullOrEmpty(condition))
                return true;

            try
            {
                // 这里实现简单的条件判断
                // 可以根据需要扩展更复杂的条件系统

                if (condition.Contains(">"))
                {
                    string[] parts = condition.Split('>');
                    if (parts.Length == 2)
                    {
                        string varName = parts[0].Trim();
                        float threshold = float.Parse(parts[1].Trim());

                        // 这里可以从游戏数据中获取变量值进行比较
                        // 示例：假设从某个数据管理器获取值
                        // float currentValue = GameEntry.DataNode.GetFloat(varName);
                        // return currentValue > threshold;

                        ColorLog.Yellow($"条件判断: {condition} (此处需要实现具体的变量获取逻辑)");
                        return true; // 临时返回true，实际使用时需要实现具体逻辑
                    }
                }

                // 可以添加更多条件类型: <, ==, !=, contains, etc.

                return true;
            }
            catch (Exception e)
            {
                ColorLog.Red($"条件评估失败: {condition}, 错误: {e.Message}");
                return false;
            }
        }

        #endregion

        #region 新增动作处理器

        /// <summary>
        /// 延迟动作
        /// 参数: [delay, 延迟时间(秒)]
        /// </summary>
        void HandleDelay(GotoChainData chainData)
        {
            // if (parameters.Length < 2)
            // {
            //     ColorLog.Red("delay 参数不足，需要延迟时间");
            //     return;
            // }

            // if (float.TryParse(parameters[1], out float delayTime))
            // {
            //     ColorLog.Green($"延迟执行: {delayTime} 秒");
            //     // 延迟逻辑在链式处理中实现
            // }
            // else
            // {
            //     ColorLog.Red($"延迟时间格式错误: {parameters[1]}");
            // }
        }

        /// <summary>
        /// 等待UI关闭
        /// 参数: [wait_ui_close, UI名称]
        /// </summary>
        void HandleWaitUIClose(GotoChainData chainData)
        {
            // if (parameters.Length < 2)
            // {
            //     ColorLog.Red("wait_ui_close 参数不足，需要UI名称");
            //     return;
            // }

            // string uiName = parameters[1];
            // ColorLog.Green($"等待UI关闭: {uiName}");
        }

        /// <summary>
        /// 异步显示UI
        /// </summary>
        IEnumerator HandleShowUIAsync(GotoChainData chainData)
        {
            // if (parameters.Length < 2)
            // {
            //     ColorLog.Red("show_ui_async 参数不足，至少需要UI名称");
            //     yield break;
            // }

            int chainId = chainData.id;
            go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(chainId);
            string uiName = chainData.param1;

            ColorLog.Green($"异步显示UI: {uiName}");

            bool success = true;
            try
            {
                EnumUIForm enumUIForm = (EnumUIForm)Enum.Parse(typeof(EnumUIForm), uiName);
                GameEntry.UI.OpenUIForm(enumUIForm);
            }
            catch (Exception e)
            {
                ColorLog.Red($"异步显示UI失败: {uiName}, 错误: {e.Message}");
                success = false;
            }

            if (success)
            {
                // 等待UI完全打开
                yield return new WaitForSeconds(0.1f);

                curNext = chainConfig.next;
                ColorLog.Green($"UI {uiName} 异步显示完成");
            }
        }

        /// <summary>
        /// 异步加载场景
        /// </summary>
        IEnumerator HandleLoadSceneAsync(GotoChainData chainData)
        {
            if (chainData.param1 == null)
            {
                ColorLog.Red("load_scene_async 参数不足，需要场景名称");
                yield break;
            }

            string sceneName = chainData.param1;
            ColorLog.Green($"异步加载场景: {sceneName}");

            try
            {
                // 这里可以实现异步场景加载逻辑
                GameEntry.Scene.LoadScene(sceneName);

                // 等待场景加载完成
                // yield return new WaitForSeconds(1f);

                ColorLog.Green($"场景 {sceneName} 异步加载完成");
            }
            catch (Exception e)
            {
                ColorLog.Red($"异步加载场景失败: {sceneName}, 错误: {e.Message}");
            }
        }

        /// <summary>
        /// 等待指定秒数
        /// </summary>
        IEnumerator HandleWaitSeconds(GotoChainData chainData)
        {
            if (chainData.param1 == null)
            {
                ColorLog.Red("wait_seconds 参数不足，需要等待时间");
                yield break;
            }

            int chainId = chainData.id;
            go_to_chain chainConfig = GameEntry.LDLTable.GetTableById<go_to_chain>(chainId);

            if (float.TryParse(chainData.param1, out float waitTime))
            {
                ColorLog.Green($"等待 {waitTime} 秒");
                yield return new WaitForSeconds(waitTime);
                curNext = chainConfig.next;
                ColorLog.Green($"等待完成");
            }
            else
            {
                ColorLog.Red($"等待时间格式错误: {chainData.param1}");
            }
        }

        #endregion

        #region 通用事件等待方法

        /// <summary>
        /// 事件等待结果
        /// </summary>
        /// <typeparam name="T">目标对象类型</typeparam>
        public class EventWaitResult<T>
        {
            public bool success;
            public T targetObject;
            public string errorMessage;
            public bool timedOut;
        }

        /// <summary>
        /// 简化的UI等待方法，通过字段返回结果
        /// </summary>
        /// <param name="enumUIForm">UI界面枚举</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认5秒</param>
        /// <returns>等待结果</returns>
        private EventWaitResult<object> lastWaitResult;

        IEnumerator WaitForUIFormLoadedAsync(EnumUIForm enumUIForm, float timeoutSeconds = 5.0f)
        {
            var result = new EventWaitResult<object>();
            bool completed = false;
            float startTime = Time.time;
            string expectedTypeName = enumUIForm.ToString();

            // 事件处理器：监听UI加载成功事件
            void onUIFormLoadSuccess(object sender, GameEventArgs e)
            {
                OpenUIFormSuccessEventArgs eventArgs = (OpenUIFormSuccessEventArgs)e;
                if (eventArgs.UIForm != null && eventArgs.UIForm.Logic != null)
                {
                    string actualTypeName = eventArgs.UIForm.Logic.GetType().Name;
                    if (actualTypeName == expectedTypeName)
                    {
                        result.targetObject = eventArgs.UIForm.Logic;
                        result.success = true;
                        completed = true;
                        ColorLog.Green($"{expectedTypeName} 加载完成");
                    }
                }
            }

            // 订阅UI加载成功事件
            GameEntry.Event.Subscribe(OpenUIFormSuccessEventArgs.EventId, onUIFormLoadSuccess);

            try
            {
                // 检查UI是否已经打开
                UGuiForm uGuiForm = GameEntry.UI.GetUIForm(enumUIForm);
                if (uGuiForm != null && uGuiForm.UIForm != null && uGuiForm.UIForm.Logic != null)
                {
                    string actualTypeName = uGuiForm.UIForm.Logic.GetType().Name;
                    if (actualTypeName == expectedTypeName)
                    {
                        result.targetObject = uGuiForm.UIForm.Logic;
                        result.success = true;
                        completed = true;
                        ColorLog.Green($"{expectedTypeName} 已经打开");
                    }
                }
                else
                {
                    ColorLog.Yellow($"{expectedTypeName} 未打开，等待其他地方打开它");
                }

                // 使用 WaitUntil 等待UI加载完成或超时
                yield return new WaitUntil(() => completed || (Time.time - startTime) > timeoutSeconds);

                // 检查是否超时
                if (!completed)
                {
                    result.success = false;
                    result.timedOut = true;
                    result.errorMessage = $"{expectedTypeName} 等待超时 ({timeoutSeconds}秒)";
                    ColorLog.Red(result.errorMessage);
                }
            }
            finally
            {
                // 取消订阅事件
                GameEntry.Event.Unsubscribe(OpenUIFormSuccessEventArgs.EventId, onUIFormLoadSuccess);
            }

            // 保存结果到字段
            lastWaitResult = result;
        }

        /// <summary>
        /// 通用的事件等待方法 - 等待指定类型的游戏事件
        /// </summary>
        /// <typeparam name="TEventArgs">事件参数类型</typeparam>
        /// <param name="eventId">事件ID</param>
        /// <param name="conditionCheck">条件检查函数</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认5秒</param>
        /// <returns>等待结果</returns>
        IEnumerator WaitForGameEventAsync<TEventArgs>(int eventId, Func<TEventArgs, bool> conditionCheck, float timeoutSeconds = 5.0f) 
            where TEventArgs : GameEventArgs
        {
            var result = new EventWaitResult<object>();
            bool completed = false;
            float startTime = Time.time;

            // 事件处理器
            void eventHandler(object sender, GameEventArgs e)
            {
                if (e is TEventArgs eventArgs && conditionCheck(eventArgs))
                {
                    result.targetObject = eventArgs;
                    result.success = true;
                    completed = true;
                    ColorLog.Green($"事件 {typeof(TEventArgs).Name} 触发并满足条件");
                }
            }

            // 订阅事件
            GameEntry.Event.Subscribe(eventId, eventHandler);

            try
            {
                // 使用 WaitUntil 等待事件触发或超时
                yield return new WaitUntil(() => completed || (Time.time - startTime) > timeoutSeconds);

                // 检查是否超时
                if (!completed)
                {
                    result.success = false;
                    result.timedOut = true;
                    result.errorMessage = $"等待事件 {typeof(TEventArgs).Name} 超时 ({timeoutSeconds}秒)";
                    ColorLog.Red(result.errorMessage);
                }
            }
            finally
            {
                // 取消订阅事件
                GameEntry.Event.Unsubscribe(eventId, eventHandler);
            }

            // 保存结果到字段
            lastWaitResult = result;
        }

        /// <summary>
        /// 通用的条件等待方法
        /// </summary>
        /// <param name="conditionCheck">条件检查函数</param>
        /// <param name="timeoutSeconds">超时时间（秒），默认5秒</param>
        /// <param name="checkInterval">检查间隔时间（秒），默认0.1秒</param>
        /// <returns>等待结果</returns>
        IEnumerator WaitForConditionAsync(Func<bool> conditionCheck, float timeoutSeconds = 5.0f, float checkInterval = 0.1f)
        {
            var result = new EventWaitResult<object>();
            float startTime = Time.time;

            while (Time.time - startTime < timeoutSeconds)
            {
                if (conditionCheck())
                {
                    result.success = true;
                    lastWaitResult = result;
                    yield break;
                }

                yield return new WaitForSeconds(checkInterval);
            }

            // 超时
            result.success = false;
            result.timedOut = true;
            result.errorMessage = $"等待条件满足超时 ({timeoutSeconds}秒)";
            ColorLog.Red(result.errorMessage);

            // 保存结果到字段
            lastWaitResult = result;
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 等待条件满足（同步版本，用于简单条件检查）
        /// </summary>
        void HandleWaitCondition(GotoChainData chainData)
        {
            string condition = chainData.condition;

            // 这里可以实现简单的条件检查逻辑
            bool conditionMet = EvaluateCondition(condition);
            
            if (conditionMet)
            {
                curNext = chainData.next;
            }
            else
            {
                curNext = chainData.elseNext;
            }
        }

        /// <summary>
        /// 使用通用方法等待UI关闭事件的示例
        /// </summary>
        IEnumerator HandleWaitUICloseAsync(GotoChainData chainData)
        {
            if (chainData.param1 == null)
            {
                ColorLog.Red("wait_ui_close_async 参数不足，需要UI名称");
                yield break;
            }

            int chainId = chainData.id;
            string uiName = chainData.param1;

            // 使用通用的游戏事件等待方法
            yield return StartCoroutine(WaitForGameEventAsync<CloseUIFormCompleteEventArgs>(
                CloseUIFormCompleteEventArgs.EventId,
                eventArgs => eventArgs.UIFormAssetName == uiName, // 条件检查
                10.0f // 超时时间10秒
            ));

            var result = lastWaitResult;
            if (result != null && result.success)
            {
                ColorLog.Green($"UI {uiName} 关闭完成");
            }
            else
            {
                ColorLog.Red($"等待UI {uiName} 关闭失败: {result?.errorMessage}");
            }

            curNext = chainData.next;
        }

        /// <summary>
        /// 使用通用方法等待任意条件满足的示例
        /// </summary>
        IEnumerator HandleWaitCustomConditionAsync(GotoChainData chainData)
        {
            if (chainData.param1 == null)
            {
                ColorLog.Red("wait_custom_condition 参数不足");
                yield break;
            }

            int chainId = chainData.id;
            string conditionType = chainData.param1;

            // 根据不同条件类型使用通用条件等待方法
            System.Func<bool> condition = null;
            switch (conditionType)
            {
                case "player_level_5":
                    condition = () => /* GameEntry.Player.Level >= 5 */ true; // 示例条件
                    break;
                case "building_count_10":
                    condition = () => /* GameEntry.LogicData.BuildingData.GetBuildingCount() >= 10 */ true;
                    break;
                default:
                    ColorLog.Red($"未知条件类型: {conditionType}");
                    yield break;
            }

            yield return StartCoroutine(WaitForConditionAsync(condition, 30.0f, 1.0f));

            var result = lastWaitResult;
            if (result != null && result.success)
            {
                ColorLog.Green($"条件 {conditionType} 满足");
            }
            else
            {
                ColorLog.Red($"等待条件 {conditionType} 超时");
            }

            curNext = chainData.next;
        }

        /// <summary>
        /// 使用 WaitUntil 等待事件驱动的异步操作示例
        /// </summary>
        IEnumerator HandleWaitUntilEvent(GotoChainData chainData)
        {
            if (chainData.param1 == null)
            {
                ColorLog.Red("wait_until_event 参数不足");
                yield break;
            }

            int chainId = chainData.id;
            string eventName = chainData.param1;

            ColorLog.Green($"等待事件: {eventName}");

            // 重置事件状态
            bool eventTriggered = false;

            // 模拟注册事件监听（实际使用时替换为真实的事件系统）
            Action eventHandler = () => {
                eventTriggered = true;
                ColorLog.Green($"事件 {eventName} 已触发");
            };

            // 这里可以根据 eventName 注册不同的事件监听器
            // 例如: GameEntry.Event.Subscribe(eventName, eventHandler);

            // 使用 WaitUntil 等待事件触发
            yield return new WaitUntil(() => eventTriggered);

            // 清理事件监听器
            // 例如: GameEntry.Event.Unsubscribe(eventName, eventHandler);

            curNext = chainData.next;
            ColorLog.Green($"事件等待完成，继续执行下一步");
        }

        #endregion

        #region 公开API

        /// <summary>
        /// 注册自定义跳转动作
        /// </summary>
        /// <param name="key">跳转标识符</param>
        /// <param name="action">跳转动作</param>
        public void RegisterGotoAction(string key, Action<GotoChainData> action)
        {
            if (m_keyActionDic.ContainsKey(key))
            {
                ColorLog.Yellow($"覆盖已存在的跳转动作: {key}");
            }

            m_keyActionDic[key] = action;
            ColorLog.Green($"注册跳转动作: {key}");
        }

        /// <summary>
        /// 注册自定义异步跳转动作
        /// </summary>
        /// <param name="key">跳转标识符</param>
        /// <param name="asyncAction">异步跳转动作</param>
        public void RegisterAsyncGotoAction(string key, Func<GotoChainData, IEnumerator> asyncAction)
        {
            if (m_asyncActionDic.ContainsKey(key))
            {
                ColorLog.Yellow($"覆盖已存在的异步跳转动作: {key}");
            }

            m_asyncActionDic[key] = asyncAction;
            ColorLog.Green($"注册异步跳转动作: {key}");
        }

        /// <summary>
        /// 取消注册跳转动作
        /// </summary>
        /// <param name="key">跳转标识符</param>
        public void UnregisterGotoAction(string key)
        {
            bool removed = false;

            if (m_keyActionDic.ContainsKey(key))
            {
                m_keyActionDic.Remove(key);
                removed = true;
            }

            if (m_asyncActionDic.ContainsKey(key))
            {
                m_asyncActionDic.Remove(key);
                removed = true;
            }

            if (removed)
            {
                ColorLog.Green($"取消注册跳转动作: {key}");
            }
            else
            {
                ColorLog.Yellow($"要取消的跳转动作不存在: {key}");
            }
        }

        /// <summary>
        /// 获取所有已注册的跳转动作
        /// </summary>
        public string[] GetAllRegisteredActions()
        {
            var allKeys = new List<string>();
            allKeys.AddRange(m_keyActionDic.Keys);
            allKeys.AddRange(m_asyncActionDic.Keys);
            return allKeys.ToArray();
        }

        #endregion
    }
}
