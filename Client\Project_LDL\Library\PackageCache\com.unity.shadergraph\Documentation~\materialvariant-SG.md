# Material Variants

When you create materials in a project, you might want to create variations based on a single material: outfits with different color schemes, damaged and undamaged versions of scenery, or shiny and weathered instances of props.

You can use Material Variants to manage these variations. For more information on Material Variants in Unity, see [Material Variants](https://docs.unity3d.com/2022.2/Documentation/Manual/materialvariant-landingpage.html) in the Unity User Manual.

You can create a Material Variant from any Shader Graph material. For more information on how to create a Material Variant, see [Create, modify, and apply Material Variants](https://docs.unity3d.com/2022.2/Documentation/Manual/materialvariant-tasks.html) in the User Manual.
