# Matrix 2x2 Node

## Description

Defines a constant **Matrix 2x2** value in the shader.

## Ports

| Name        | Direction           | Type  | Binding | Description |
|:------------ |:-------------|:-----|:---|:---|
| Out | Output      |    Matrix 2 | None | Output value |

## Controls

| Name        | Type           | Options  | Description |
|:------------ |:-------------|:-----|:---|
|  | Matrix 2x2 |  | Sets output value |

## Generated Code Example

The following example code represents one possible outcome of this node.

```
float2x2 _Matrix2x2 = float2x2(1, 0, 0, 1);
```
