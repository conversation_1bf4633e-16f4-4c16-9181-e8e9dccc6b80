# UV Nodes

| [Flipbook](Flipbook-Node.md) | [Polar Coordinates](Polar-Coordinates-Node.md) |
| :--------------- | :-------- |
| ![Example of the Flipbook Node](images/FlipbookNodeThumb.png) | ![Example of the Polar Coordinates Node](images/PolarCoordinatesNodeThumb.png) |
| Creates a flipbook, or texture sheet animation, of the UVs supplied to input In. | Converts the value of input UV to polar coordinates. |
| [**Radial Shear**](Radial-Shear-Node.md) | [**Rotate**](Rotate-Node.md) |
| ![Example of the Radial Shear Node](images/RadialShearNodeThumb.png) | ![Example of the Rotate Node](images/RotateNodeThumb.png) |
| Applies a radial shear warping effect similar to a wave to the value of input UV. | Rotates the value of input UV around a reference point defined by input Center by the amount of input Rotation. |
| [**Spherize**](Spherize-Node.md) | [**Tiling and Offset**](Tiling-And-Offset-Node.md) |
| ![Example of the Spherize Node](images/SpherizeNodeThumb.png) | ![Example of the Tiling and Offset Node](images/TilingAndOffsetNodeThumb.png) |
| Applies a spherical warping effect similar to a fisheye camera lens to the value of input UV. | Tiles and offsets the value of input UV by the inputs Tiling and Offset respectively. |
| [**Triplanar**](Triplanar-Node.md) | [**Twirl**](Twirl-Node.md) |
| ![Example of the Triplanar Node](images/TriplanarNodeThumb.png) | ![Example of the Twirl Node](images/TwirlNodeThumb.png) |
| A method of generating UVs and sampling a texture by projecting in world space. | Applies a twirl warping effect similar to a black hole to the value of input UV. |
| [**Parallax Mapping**](Parallax-Mapping-Node.md) | [**Parallax Occlusion Mapping**](Parallax-Occlusion-Mapping-Node.md) |
| ![Example of the Parallax Mapping Node](images/ParallaxOcclusionMappingThumb.png) | ![Example of the Parallax Occlusion Mapping Node](images/ParallaxOcclusionMappingNodeThumb.PNG) |
| Creates a parallax effect that displaces a material's UVs. | Creates a parallax effect that displaces a material's UVs and depth. |
