# Integer Node

## Description

Defines a constant **Float** value in the shader using an **Integer** field. Can be converted to a **Float** type [Property](Property-Types.md) with a **Mode** setting of **Integer** via the [Node's](Node.md) context menu.

## Ports

| Name        | Direction           | Type  | Binding | Description |
|:------------ |:-------------|:-----|:---|:---|
| Out | Output      |    Float    | None | Output value |

## Controls

| Name        | Type           | Options  | Description |
|:------------ |:-------------|:-----|:---|
|       | Integer |  | Defines the output value. |

## Generated Code Example

The following example code represents one possible outcome of this node.

```
float _Integer = 1;
```
